package com.scb.api.ent.payments.unit.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scb.api.common.framework.exception.GenericRuntimeException;
import com.scb.api.ent.payments.PaymentRequest;
import com.scb.api.ent.payments.PaymentResponse;
import com.scb.api.ent.payments.controller.PaymentsAPI;
import com.scb.api.ent.payments.impl.PaymentsAPIImpl;
import com.scb.api.ent.payments.onlineBiller.request.OnlineBillerInquiryRequest;
import com.scb.api.ent.payments.onlineBiller.response.OnlineBillerInquiryResponse;
import com.scb.api.ent.payments.unit.BaseUnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for PaymentsAPI controller
 * Tests all API endpoints with various scenarios including success, validation errors, and exceptions
 */
@RunWith(MockitoJUnitRunner.class)
public class PaymentsAPITest extends BaseUnitTest {

    @Mock
    private PaymentsAPIImpl controllerImpl;

    @InjectMocks
    private PaymentsAPI paymentsAPI;

    private ObjectMapper objectMapper;
    private MockHttpServletRequest mockRequest;

    @Before
    public void setUp() {
        objectMapper = new ObjectMapper();
        mockRequest = new MockHttpServletRequest();
        mockRequest.setMethod("POST");
        mockRequest.setRequestURI("/v1/payment/billPayments");
    }

    @Test
    public void testPayment_Success() throws Exception {
        // Given
        String reqUID = "TEST123456789";
        String ownerID = "OWNER001";
        String sourceSystem = "ENET";
        PaymentRequest paymentRequest = createValidPaymentRequest();
        PaymentResponse expectedResponse = createValidPaymentResponse();

        when(controllerImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest))
                .thenReturn(expectedResponse);

        // When
        PaymentResponse actualResponse = paymentsAPI.payment(
                reqUID, ownerID, sourceSystem, paymentRequest, mockRequest);

        // Then
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(controllerImpl).payment(reqUID, ownerID, sourceSystem, paymentRequest);
    }

    @Test(expected = GenericRuntimeException.class)
    public void testPayment_ThrowsException() throws Exception {
        // Given
        String reqUID = "TEST123456789";
        String ownerID = "OWNER001";
        String sourceSystem = "ENET";
        PaymentRequest paymentRequest = createValidPaymentRequest();

        when(controllerImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest))
                .thenThrow(new GenericRuntimeException("Payment processing failed"));

        // When
        paymentsAPI.payment(reqUID, ownerID, sourceSystem, paymentRequest, mockRequest);

        // Then - exception expected
    }

    @Test
    public void testOnlineBillerVerification_Success() throws Exception {
        // Given
        String reqUID = "TEST123456789";
        String ownerID = "OWNER001";
        String sourceSystem = "ENET";
        OnlineBillerInquiryRequest billerInquiry = createValidBillerInquiryRequest();
        OnlineBillerInquiryResponse expectedResponse = createValidBillerInquiryResponse();
        ResponseEntity<OnlineBillerInquiryResponse> expectedEntity = 
                new ResponseEntity<>(expectedResponse, HttpStatus.OK);

        when(controllerImpl.onlineBillerVerification(reqUID, ownerID, sourceSystem, billerInquiry))
                .thenReturn(expectedEntity);

        // When
        ResponseEntity<OnlineBillerInquiryResponse> actualResponse = 
                paymentsAPI.onlineBillerVerification(reqUID, ownerID, sourceSystem, billerInquiry, mockRequest);

        // Then
        assertNotNull(actualResponse);
        assertEquals(HttpStatus.OK, actualResponse.getStatusCode());
        assertEquals(expectedResponse, actualResponse.getBody());
        verify(controllerImpl).onlineBillerVerification(reqUID, ownerID, sourceSystem, billerInquiry);
    }

    @Test(expected = Exception.class)
    public void testOnlineBillerVerification_ThrowsException() throws Exception {
        // Given
        String reqUID = "TEST123456789";
        String ownerID = "OWNER001";
        String sourceSystem = "ENET";
        OnlineBillerInquiryRequest billerInquiry = createValidBillerInquiryRequest();

        when(controllerImpl.onlineBillerVerification(reqUID, ownerID, sourceSystem, billerInquiry))
                .thenThrow(new Exception("Biller verification failed"));

        // When
        paymentsAPI.onlineBillerVerification(reqUID, ownerID, sourceSystem, billerInquiry, mockRequest);

        // Then - exception expected
    }

    @Test
    public void testPayment_WithNullRequest() throws Exception {
        // Given
        String reqUID = "TEST123456789";
        String ownerID = "OWNER001";
        String sourceSystem = "ENET";
        PaymentRequest paymentRequest = null;

        // When/Then
        try {
            paymentsAPI.payment(reqUID, ownerID, sourceSystem, paymentRequest, mockRequest);
            fail("Expected exception for null request");
        } catch (Exception e) {
            // Expected behavior - validation should catch this
        }
    }

    @Test
    public void testPayment_WithEmptyHeaders() throws Exception {
        // Given
        String reqUID = "";
        String ownerID = "";
        String sourceSystem = "";
        PaymentRequest paymentRequest = createValidPaymentRequest();

        // When/Then
        try {
            paymentsAPI.payment(reqUID, ownerID, sourceSystem, paymentRequest, mockRequest);
            fail("Expected exception for empty headers");
        } catch (Exception e) {
            // Expected behavior - validation should catch this
        }
    }
}
