package com.scb.api.ent.payments.unit;

import com.scb.api.ent.payments.*;
import com.scb.api.ent.payments.onlineBiller.request.OnlineBillerInquiryRequest;
import com.scb.api.ent.payments.onlineBiller.response.OnlineBillerInquiryResponse;
import com.scb.api.ent.payments.v2.*;
import com.scb.api.ent.payments.v3.*;
import com.scb.api.ent.payments.v4.*;
import org.junit.Before;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * Base class for unit tests providing common test data creation methods
 * and utilities for payment service testing
 */
public abstract class BaseUnitTest {

    protected Random random;
    protected DateTimeFormatter dateFormatter;

    @Before
    public void baseSetUp() {
        random = new Random();
        dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    }

    // ==================== V1 Payment Request/Response Builders ====================

    protected PaymentRequest createValidPaymentRequest() {
        PaymentRequest request = new PaymentRequest();
        request.setTransactionInfo(createValidTransactionInfo());
        request.setImstPayment(createValidIMSTPayment());
        return request;
    }

    protected PaymentResponse createValidPaymentResponse() {
        PaymentResponse response = new PaymentResponse();
        response.setImstPaymentResponse(createValidIMSTPaymentResponse());
        return response;
    }

    protected TransactionInfo createValidTransactionInfo() {
        TransactionInfo info = new TransactionInfo();
        info.setTransactionType("PAYMENT");
        info.setProductCode("PTNR");
        info.setTransactionCode("LNPY");
        return info;
    }

    protected IMSTPayment createValidIMSTPayment() {
        IMSTPayment payment = new IMSTPayment();
        payment.setTransactionOwner(createValidTransactionOwner());
        payment.setPaymentInfo(createValidPaymentInfo());
        payment.setBillerInfo(createValidBillerInfo());
        payment.setProcessingDate(LocalDate.now().format(dateFormatter));
        return payment;
    }

    protected IMSTPaymentResponse createValidIMSTPaymentResponse() {
        IMSTPaymentResponse response = new IMSTPaymentResponse();
        response.setAccountId("***********");
        response.setOcCode("3261");
        response.setPostNextDayFlag("N");
        response.setProcessingDate(LocalDate.now().format(dateFormatter));
        response.setProcessingTime("12:30:45");
        return response;
    }

    // ==================== V2 Payment Request/Response Builders ====================

    protected PaymentRequestV2 createValidPaymentRequestV2() {
        PaymentRequestV2 request = new PaymentRequestV2();
        request.setOrgRqUid(generateRequestUID());
        request.setTransactionInfo(createValidTransactionInfo());
        request.setImstPayment(createValidIMSTPaymentV2());
        return request;
    }

    protected PaymentResponseV2 createValidPaymentResponseV2() {
        PaymentResponseV2 response = new PaymentResponseV2();
        response.setImstPaymentResponse(createValidIMSTPaymentResponseV2());
        response.setLoanAccount(createValidLoanAccountData());
        return response;
    }

    protected IMSTPaymentV2 createValidIMSTPaymentV2() {
        IMSTPaymentV2 payment = new IMSTPaymentV2();
        payment.setTransactionOwner(createValidTransactionOwner());
        payment.setPaymentInfo(createValidPaymentInfo());
        payment.setBillerInfo(createValidBillerInfo());
        payment.setProcessingDate(LocalDate.now().format(dateFormatter));
        return payment;
    }

    protected IMSTPaymentResponseV2 createValidIMSTPaymentResponseV2() {
        IMSTPaymentResponseV2 response = new IMSTPaymentResponseV2();
        response.setAccountId("***********");
        response.setOcCode("3261");
        response.setPostNextDayFlag("N");
        response.setProcessingDate(LocalDate.now().format(dateFormatter));
        response.setProcessingTime("12:30:45");
        return response;
    }

    // ==================== V3 Payment Request/Response Builders ====================

    protected PaymentRequestV3 createValidPaymentRequestV3() {
        PaymentRequestV3 request = new PaymentRequestV3();
        request.setOrgRqUid(generateRequestUID());
        request.setTransactionInfo(createValidTransactionInfoV3());
        request.setImstPayment(createValidIMSTPaymentV3());
        return request;
    }

    protected PaymentResponseV3 createValidPaymentResponseV3() {
        PaymentResponseV3 response = new PaymentResponseV3();
        response.setImstPaymentResponse(createValidIMSTPaymentResponseV3());
        response.setLoanAccount(createValidLoanAccountDataV3());
        response.setStatus(createValidStatusV3());
        return response;
    }

    protected TransactionInfoV3 createValidTransactionInfoV3() {
        TransactionInfoV3 info = new TransactionInfoV3();
        info.setTransactionType("PAYMENT");
        info.setProductCode("PTNR");
        info.setTransactionCode("LNPY");
        return info;
    }

    protected IMSTPaymentV3 createValidIMSTPaymentV3() {
        IMSTPaymentV3 payment = new IMSTPaymentV3();
        payment.setTransactionOwner(createValidTransactionOwner());
        payment.setPaymentInfo(createValidPaymentInfo());
        payment.setBillerInfo(createValidBillerInfo());
        payment.setProcessingDate(LocalDate.now().format(dateFormatter));
        payment.setPaymentValueDate(LocalDate.now().format(dateFormatter));
        payment.setDepositAcctFrom("DSC-**********");
        payment.setAnnotation("Test payment annotation");
        return payment;
    }

    protected IMSTPaymentResponseV3 createValidIMSTPaymentResponseV3() {
        IMSTPaymentResponseV3 response = new IMSTPaymentResponseV3();
        response.setAccountId("***********");
        response.setOcCode("3261");
        response.setPostNextDayFlag("N");
        response.setProcessingDate(LocalDate.now().format(dateFormatter));
        response.setProcessingTime("12:30:45");
        return response;
    }

    protected LoanAccountDataV3 createValidLoanAccountDataV3() {
        LoanAccountDataV3 loanAccount = new LoanAccountDataV3();
        loanAccount.setPrincipal(BigDecimal.valueOf(1000.00));
        loanAccount.setInterest(BigDecimal.valueOf(50.00));
        loanAccount.setOtherFee(BigDecimal.valueOf(25.00));
        return loanAccount;
    }

    protected StatusV3 createValidStatusV3() {
        StatusV3 status = new StatusV3();
        status.setStatusCode("0");
        status.setSeverity("INFO");
        status.setStatusDesc("successful");
        return status;
    }

    // ==================== V4 Payment Request/Response Builders ====================

    protected PaymentRequestV4 createValidPaymentRequestV4() {
        PaymentRequestV4 request = new PaymentRequestV4();
        request.setOrgRqUid(generateRequestUID());
        request.setTransactionInfo(createValidTransactionInfoV4());
        request.setImstPayment(createValidIMSTPaymentV4());
        return request;
    }

    protected PaymentResponseV4 createValidPaymentResponseV4() {
        PaymentResponseV4 response = new PaymentResponseV4();
        response.setImstPaymentResponse(createValidIMSTPaymentResponseV4());
        response.setLoanAccount(createValidLoanAccountDataV4());
        response.setStatus(createValidStatusV4());
        return response;
    }

    protected TransactionInfoV4 createValidTransactionInfoV4() {
        TransactionInfoV4 info = new TransactionInfoV4();
        info.setTransactionType("PAYMENT");
        info.setProductCode("PTNR");
        info.setTransactionCode("LNPY");
        return info;
    }

    protected IMSTPaymentV4 createValidIMSTPaymentV4() {
        IMSTPaymentV4 payment = new IMSTPaymentV4();
        payment.setTransactionOwner(createValidTransactionOwner());
        payment.setPaymentInfo(createValidPaymentInfo());
        payment.setBillerInfo(createValidBillerInfo());
        payment.setProcessingDate(LocalDate.now().format(dateFormatter));
        payment.setPaymentValueDate(LocalDate.now().format(dateFormatter));
        payment.setDepositAcctFrom("DSC-**********");
        payment.setAnnotation("Test payment annotation");
        return payment;
    }

    protected IMSTPaymentResponseV4 createValidIMSTPaymentResponseV4() {
        IMSTPaymentResponseV4 response = new IMSTPaymentResponseV4();
        response.setAccountId("***********");
        response.setOcCode("3261");
        response.setPostNextDayFlag("N");
        response.setProcessingDate(LocalDate.now().format(dateFormatter));
        response.setProcessingTime("12:30:45");
        return response;
    }

    protected LoanAccountDataV4 createValidLoanAccountDataV4() {
        LoanAccountDataV4 loanAccount = new LoanAccountDataV4();
        loanAccount.setPrincipal(BigDecimal.valueOf(1000.00));
        loanAccount.setInterest(BigDecimal.valueOf(50.00));
        loanAccount.setOtherFee(BigDecimal.valueOf(25.00));
        return loanAccount;
    }

    protected StatusV4 createValidStatusV4() {
        StatusV4 status = new StatusV4();
        status.setStatusCode("0");
        status.setSeverity("INFO");
        status.setStatusDesc("successful");
        return status;
    }

    // ==================== Common Component Builders ====================

    protected TransactionOwner createValidTransactionOwner() {
        TransactionOwner owner = new TransactionOwner();
        owner.setTerminalNumber("2550");
        owner.setDeviceId("**********");
        return owner;
    }

    protected PaymentInfo createValidPaymentInfo() {
        PaymentInfo info = new PaymentInfo();
        info.setPaymentAmount(BigDecimal.valueOf(1000.00));
        info.setPaymentInstrumentType("ETFR");
        info.setDepositAccountInfo(createValidDepositAccountInfo());
        return info;
    }

    protected DepositAccountInfo createValidDepositAccountInfo() {
        DepositAccountInfo accountInfo = new DepositAccountInfo();
        accountInfo.setAccountId("**********");
        return accountInfo;
    }

    protected BillerInfo createValidBillerInfo() {
        BillerInfo billerInfo = new BillerInfo();
        billerInfo.setBillerAccountInfo(createValidBillerAccountInfo());
        billerInfo.setBillerName("Speedy Loan");
        return billerInfo;
    }

    protected BillerAccountInfo createValidBillerAccountInfo() {
        BillerAccountInfo accountInfo = new BillerAccountInfo();
        accountInfo.setAccountId("***********");
        accountInfo.setAccountType("LNA");
        accountInfo.setAccountCurrency("764");
        return accountInfo;
    }

    protected LoanAccountData createValidLoanAccountData() {
        LoanAccountData loanAccount = new LoanAccountData();
        loanAccount.setPrincipal(BigDecimal.valueOf(1000.00));
        loanAccount.setInterest(BigDecimal.valueOf(50.00));
        loanAccount.setOtherFee(BigDecimal.valueOf(25.00));
        return loanAccount;
    }

    // ==================== Online Biller Builders ====================

    protected OnlineBillerInquiryRequest createValidBillerInquiryRequest() {
        OnlineBillerInquiryRequest request = new OnlineBillerInquiryRequest();
        // Add specific fields based on actual OnlineBillerInquiryRequest structure
        return request;
    }

    protected OnlineBillerInquiryResponse createValidBillerInquiryResponse() {
        OnlineBillerInquiryResponse response = new OnlineBillerInquiryResponse();
        // Add specific fields based on actual OnlineBillerInquiryResponse structure
        return response;
    }

    // ==================== Utility Methods ====================

    protected String generateRequestUID() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    protected String generateAccountNumber() {
        return String.format("%010d", random.nextInt(**********));
    }

    protected BigDecimal generateRandomAmount() {
        return BigDecimal.valueOf(random.nextDouble() * 10000).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    protected String getCurrentDate() {
        return LocalDate.now().format(dateFormatter);
    }

    protected String getFutureDate(int daysFromNow) {
        return LocalDate.now().plusDays(daysFromNow).format(dateFormatter);
    }

    protected String getPastDate(int daysAgo) {
        return LocalDate.now().minusDays(daysAgo).format(dateFormatter);
    }
}
