package com.scb.api.ent.payments.testdata;

import com.scb.api.ent.payments.*;
import com.scb.api.ent.payments.v2.*;
import com.scb.api.ent.payments.v3.*;
import com.scb.api.ent.payments.v4.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Mock data generator for payment service testing
 * Provides comprehensive test data for various payment scenarios including
 * valid cases, edge cases, error conditions, and boundary testing
 */
public class PaymentMockDataGenerator {

    private static final Random RANDOM = new Random();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    // Valid test data constants
    private static final String[] VALID_TRANSACTION_TYPES = {"PAYMENT", "REVPAYMENTTXN", "ATMTXN"};
    private static final String[] VALID_PRODUCT_CODES = {"PTNR", "LOAN", "CARD", "DEPO"};
    private static final String[] VALID_TRANSACTION_CODES = {"LNPY", "CRDT", "DEBT", "TRAN"};
    private static final String[] VALID_PAYMENT_INSTRUMENTS = {"ETFR", "CARD", "CASH", "CHQ"};
    private static final String[] VALID_ACCOUNT_TYPES = {"LNA", "SDA", "CDA", "TDA"};
    private static final String[] VALID_CURRENCY_CODES = {"764", "840", "978", "392", "156", "826"};
    private static final String[] VALID_SOURCE_SYSTEMS = {"ENET", "PAYH", "CHAT", "IVR", "SDO", "STEL"};
    private static final String[] VALID_TERMINAL_NUMBERS = {"2550", "2551", "2552", "1111", "ws07"};
    private static final String[] VALID_DEVICE_IDS = {"0*********", "**********", "**********", "**********"};
    
    // Invalid test data constants
    private static final String[] INVALID_TRANSACTION_TYPES = {"", null, "INVALID", "UNKNOWN"};
    private static final String[] INVALID_CURRENCY_CODES = {"", null, "999", "ABC", "76", "7644"};
    private static final String[] INVALID_ACCOUNT_NUMBERS = {"", null, "*********", "***********", "*********a"};
    private static final BigDecimal[] INVALID_AMOUNTS = {null, BigDecimal.ZERO, BigDecimal.valueOf(-100)};

    // ==================== Valid Payment Request Generators ====================

    public static PaymentRequest generateValidPaymentRequest() {
        PaymentRequest request = new PaymentRequest();
        request.setTransactionInfo(generateValidTransactionInfo());
        request.setImstPayment(generateValidIMSTPayment());
        return request;
    }

    public static PaymentRequestV2 generateValidPaymentRequestV2() {
        PaymentRequestV2 request = new PaymentRequestV2();
        request.setOrgRqUid(generateRequestUID());
        request.setTransactionInfo(generateValidTransactionInfo());
        request.setImstPayment(generateValidIMSTPaymentV2());
        request.setPromptPayInfo(generateValidPromptPayInfo());
        return request;
    }

    public static PaymentRequestV3 generateValidPaymentRequestV3() {
        PaymentRequestV3 request = new PaymentRequestV3();
        request.setOrgRqUid(generateRequestUID());
        request.setTransactionInfo(generateValidTransactionInfoV3());
        request.setImstPayment(generateValidIMSTPaymentV3());
        request.setPromptPayInfo(generateValidPromptPayInfo());
        return request;
    }

    public static PaymentRequestV4 generateValidPaymentRequestV4() {
        PaymentRequestV4 request = new PaymentRequestV4();
        request.setOrgRqUid(generateRequestUID());
        request.setTransactionInfo(generateValidTransactionInfoV4());
        request.setImstPayment(generateValidIMSTPaymentV4());
        request.setPromptPayInfo(generateValidPromptPayInfo());
        return request;
    }

    // ==================== Valid Component Generators ====================

    public static TransactionInfo generateValidTransactionInfo() {
        TransactionInfo info = new TransactionInfo();
        info.setTransactionType(getRandomElement(VALID_TRANSACTION_TYPES));
        info.setProductCode(getRandomElement(VALID_PRODUCT_CODES));
        info.setTransactionCode(getRandomElement(VALID_TRANSACTION_CODES));
        return info;
    }

    public static TransactionInfoV3 generateValidTransactionInfoV3() {
        TransactionInfoV3 info = new TransactionInfoV3();
        info.setTransactionType(getRandomElement(VALID_TRANSACTION_TYPES));
        info.setProductCode(getRandomElement(VALID_PRODUCT_CODES));
        info.setTransactionCode(getRandomElement(VALID_TRANSACTION_CODES));
        return info;
    }

    public static TransactionInfoV4 generateValidTransactionInfoV4() {
        TransactionInfoV4 info = new TransactionInfoV4();
        info.setTransactionType(getRandomElement(VALID_TRANSACTION_TYPES));
        info.setProductCode(getRandomElement(VALID_PRODUCT_CODES));
        info.setTransactionCode(getRandomElement(VALID_TRANSACTION_CODES));
        return info;
    }

    public static IMSTPayment generateValidIMSTPayment() {
        IMSTPayment payment = new IMSTPayment();
        payment.setTransactionOwner(generateValidTransactionOwner());
        payment.setPaymentInfo(generateValidPaymentInfo());
        payment.setBillerInfo(generateValidBillerInfo());
        payment.setBillInfo(generateValidBillInfo());
        payment.setProcessingDate(getCurrentDate());
        return payment;
    }

    public static IMSTPaymentV2 generateValidIMSTPaymentV2() {
        IMSTPaymentV2 payment = new IMSTPaymentV2();
        payment.setTransactionOwner(generateValidTransactionOwner());
        payment.setPaymentInfo(generateValidPaymentInfo());
        payment.setBillerInfo(generateValidBillerInfo());
        payment.setBillInfo(generateValidBillInfo());
        payment.setProcessingDate(getCurrentDate());
        return payment;
    }

    public static IMSTPaymentV3 generateValidIMSTPaymentV3() {
        IMSTPaymentV3 payment = new IMSTPaymentV3();
        payment.setTransactionOwner(generateValidTransactionOwner());
        payment.setPaymentInfo(generateValidPaymentInfo());
        payment.setBillerInfo(generateValidBillerInfo());
        payment.setBillInfo(generateValidBillInfo());
        payment.setProcessingDate(getCurrentDate());
        payment.setPaymentValueDate(getFutureDate(1));
        payment.setDepositAcctFrom("DSC-" + generateAccountNumber());
        payment.setAnnotation("Test payment annotation " + RANDOM.nextInt(1000));
        return payment;
    }

    public static IMSTPaymentV4 generateValidIMSTPaymentV4() {
        IMSTPaymentV4 payment = new IMSTPaymentV4();
        payment.setTransactionOwner(generateValidTransactionOwner());
        payment.setPaymentInfo(generateValidPaymentInfo());
        payment.setBillerInfo(generateValidBillerInfo());
        payment.setBillInfo(generateValidBillInfo());
        payment.setProcessingDate(getCurrentDate());
        payment.setPaymentValueDate(getFutureDate(1));
        payment.setDepositAcctFrom("DSC-" + generateAccountNumber());
        payment.setAnnotation("Test payment annotation " + RANDOM.nextInt(1000));
        return payment;
    }

    public static TransactionOwner generateValidTransactionOwner() {
        TransactionOwner owner = new TransactionOwner();
        owner.setTerminalNumber(getRandomElement(VALID_TERMINAL_NUMBERS));
        owner.setDeviceId(getRandomElement(VALID_DEVICE_IDS));
        return owner;
    }

    public static PaymentInfo generateValidPaymentInfo() {
        PaymentInfo info = new PaymentInfo();
        info.setPaymentAmount(generateRandomAmount());
        info.setPaymentInstrumentType(getRandomElement(VALID_PAYMENT_INSTRUMENTS));
        info.setDepositAccountInfo(generateValidDepositAccountInfo());
        return info;
    }

    public static DepositAccountInfo generateValidDepositAccountInfo() {
        DepositAccountInfo accountInfo = new DepositAccountInfo();
        accountInfo.setAccountId(generateAccountNumber());
        return accountInfo;
    }

    public static BillerInfo generateValidBillerInfo() {
        BillerInfo billerInfo = new BillerInfo();
        billerInfo.setBillerAccountInfo(generateValidBillerAccountInfo());
        billerInfo.setBillerName("Test Biller " + RANDOM.nextInt(1000));
        return billerInfo;
    }

    public static BillerAccountInfo generateValidBillerAccountInfo() {
        BillerAccountInfo accountInfo = new BillerAccountInfo();
        accountInfo.setAccountId(generateAccountNumber());
        accountInfo.setAccountType(getRandomElement(VALID_ACCOUNT_TYPES));
        accountInfo.setAccountCurrency(getRandomElement(VALID_CURRENCY_CODES));
        return accountInfo;
    }

    public static BillInfo generateValidBillInfo() {
        BillInfo billInfo = new BillInfo();
        // Add specific bill info fields based on actual BillInfo structure
        return billInfo;
    }

    public static PromptPayInfo generateValidPromptPayInfo() {
        PromptPayInfo promptPayInfo = new PromptPayInfo();
        // Add specific PromptPay fields based on actual PromptPayInfo structure
        return promptPayInfo;
    }

    public static ATMPayment generateValidATMPayment() {
        ATMPayment atmPayment = new ATMPayment();
        // Add specific ATM payment fields based on actual ATMPayment structure
        return atmPayment;
    }

    // ==================== Invalid Data Generators ====================

    public static PaymentRequest generateInvalidPaymentRequest(String invalidField) {
        PaymentRequest request = generateValidPaymentRequest();
        
        switch (invalidField) {
            case "NULL_TRANSACTION_INFO":
                request.setTransactionInfo(null);
                break;
            case "NULL_IMST_PAYMENT":
                request.setImstPayment(null);
                break;
            case "INVALID_TRANSACTION_TYPE":
                request.getTransactionInfo().setTransactionType(getRandomElement(INVALID_TRANSACTION_TYPES));
                break;
            case "INVALID_AMOUNT":
                request.getImstPayment().getPaymentInfo().setPaymentAmount(getRandomElement(INVALID_AMOUNTS));
                break;
            case "INVALID_ACCOUNT_NUMBER":
                request.getImstPayment().getPaymentInfo().getDepositAccountInfo()
                       .setAccountId(getRandomElement(INVALID_ACCOUNT_NUMBERS));
                break;
            case "INVALID_CURRENCY":
                request.getImstPayment().getBillerInfo().getBillerAccountInfo()
                       .setAccountCurrency(getRandomElement(INVALID_CURRENCY_CODES));
                break;
        }
        
        return request;
    }

    // ==================== Edge Case Generators ====================

    public static PaymentRequest generateBoundaryAmountPaymentRequest(String boundaryType) {
        PaymentRequest request = generateValidPaymentRequest();
        
        switch (boundaryType) {
            case "MIN_AMOUNT":
                request.getImstPayment().getPaymentInfo().setPaymentAmount(BigDecimal.valueOf(0.01));
                break;
            case "MAX_AMOUNT":
                request.getImstPayment().getPaymentInfo().setPaymentAmount(BigDecimal.valueOf(999999.99));
                break;
            case "LARGE_DECIMAL":
                request.getImstPayment().getPaymentInfo().setPaymentAmount(BigDecimal.valueOf(123.456789));
                break;
        }
        
        return request;
    }

    public static PaymentRequest generateSpecialCharacterPaymentRequest() {
        PaymentRequest request = generateValidPaymentRequest();
        request.getImstPayment().getBillerInfo().setBillerName("Test Biller with Special Chars !@#$%^&*()");
        return request;
    }

    public static List<PaymentRequest> generateBulkPaymentRequests(int count) {
        List<PaymentRequest> requests = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            requests.add(generateValidPaymentRequest());
        }
        return requests;
    }

    // ==================== Response Generators ====================

    public static PaymentResponse generateValidPaymentResponse() {
        PaymentResponse response = new PaymentResponse();
        response.setImstPaymentResponse(generateValidIMSTPaymentResponse());
        return response;
    }

    public static IMSTPaymentResponse generateValidIMSTPaymentResponse() {
        IMSTPaymentResponse response = new IMSTPaymentResponse();
        response.setAccountId(generateAccountNumber());
        response.setOcCode("3261");
        response.setPostNextDayFlag("N");
        response.setProcessingDate(getCurrentDate());
        response.setProcessingTime(getCurrentTime());
        return response;
    }

    // ==================== Utility Methods ====================

    public static String generateRequestUID() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    public static String generateAccountNumber() {
        return String.format("%010d", RANDOM.nextInt(**********));
    }

    public static BigDecimal generateRandomAmount() {
        return BigDecimal.valueOf(RANDOM.nextDouble() * 10000 + 1).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public static String getCurrentDate() {
        return LocalDate.now().format(DATE_FORMATTER);
    }

    public static String getFutureDate(int daysFromNow) {
        return LocalDate.now().plusDays(daysFromNow).format(DATE_FORMATTER);
    }

    public static String getPastDate(int daysAgo) {
        return LocalDate.now().minusDays(daysAgo).format(DATE_FORMATTER);
    }

    public static String getCurrentTime() {
        return String.format("%02d:%02d:%02d", 
                           RANDOM.nextInt(24), RANDOM.nextInt(60), RANDOM.nextInt(60));
    }

    public static String getRandomSourceSystem() {
        return getRandomElement(VALID_SOURCE_SYSTEMS);
    }

    private static <T> T getRandomElement(T[] array) {
        return array[RANDOM.nextInt(array.length)];
    }

    private static BigDecimal getRandomElement(BigDecimal[] array) {
        return array[RANDOM.nextInt(array.length)];
    }

    // ==================== Scenario-based Generators ====================

    public static PaymentRequest generateLoanPaymentScenario() {
        PaymentRequest request = generateValidPaymentRequest();
        request.getTransactionInfo().setProductCode("LOAN");
        request.getTransactionInfo().setTransactionCode("LNPY");
        request.getImstPayment().getBillerInfo().getBillerAccountInfo().setAccountType("LNA");
        return request;
    }

    public static PaymentRequest generateCardPaymentScenario() {
        PaymentRequest request = generateValidPaymentRequest();
        request.getTransactionInfo().setProductCode("CARD");
        request.getImstPayment().getPaymentInfo().setPaymentInstrumentType("CARD");
        return request;
    }

    public static PaymentRequest generateCashPaymentScenario() {
        PaymentRequest request = generateValidPaymentRequest();
        request.getImstPayment().getPaymentInfo().setPaymentInstrumentType("CASH");
        return request;
    }

    public static PaymentRequest generateHighValuePaymentScenario() {
        PaymentRequest request = generateValidPaymentRequest();
        request.getImstPayment().getPaymentInfo().setPaymentAmount(BigDecimal.valueOf(500000.00));
        return request;
    }

    public static PaymentRequest generateForeignCurrencyScenario() {
        PaymentRequest request = generateValidPaymentRequest();
        request.getImstPayment().getBillerInfo().getBillerAccountInfo().setAccountCurrency("840"); // USD
        return request;
    }
}
