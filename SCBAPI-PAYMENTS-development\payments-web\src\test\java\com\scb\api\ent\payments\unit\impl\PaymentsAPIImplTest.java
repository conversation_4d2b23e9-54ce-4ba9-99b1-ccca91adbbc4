package com.scb.api.ent.payments.unit.impl;

import com.scb.api.common.framework.exception.GenericRuntimeException;
import com.scb.api.ent.payments.PaymentRequest;
import com.scb.api.ent.payments.PaymentResponse;
import com.scb.api.ent.payments.constant.PaymentsAPICommonConstant;
import com.scb.api.ent.payments.impl.PaymentsAPIImpl;
import com.scb.api.ent.payments.impl.mapping.RequestMapper;
import com.scb.api.ent.payments.impl.mapping.ResponseMapper;
import com.scb.api.ent.payments.impl.service.ExternalService;
import com.scb.api.ent.payments.unit.BaseUnitTest;
import com.scb.api.ent.payments.validator.v1.RequestValidator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.jms.JMSException;
import java.io.UnsupportedEncodingException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for PaymentsAPIImpl service implementation
 * Tests business logic, validation, mapping, and external service integration
 */
@RunWith(MockitoJUnitRunner.class)
public class PaymentsAPIImplTest extends BaseUnitTest {

    @Mock
    private RequestValidator requestValidator;

    @Mock
    private RequestMapper requestMapper;

    @Mock
    private ResponseMapper responseMapper;

    @Mock
    private ExternalService externalService;

    @InjectMocks
    private PaymentsAPIImpl paymentsAPIImpl;

    private String reqUID;
    private String ownerID;
    private String sourceSystem;

    @Before
    public void setUp() {
        reqUID = "TEST123456789";
        ownerID = "OWNER001";
        sourceSystem = "ENET";
    }

    @Test
    public void testPayment_IMSTSupportedTransaction_Success() throws Exception {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getTransactionInfo().setTransactionType("PAYMENT");
        PaymentResponse expectedResponse = createValidPaymentResponse();

        doNothing().when(requestValidator).validateIMSTPaymentRequest(paymentRequest);
        when(requestMapper.mapToPmtSvcRq(anyString(), anyString(), anyString(), any(), any(), anyString()))
                .thenReturn("<xml>mock request</xml>");
        when(externalService.processPayment(anyString())).thenReturn("<xml>mock response</xml>");
        when(responseMapper.mapToPaymentResponse(anyString())).thenReturn(expectedResponse);

        // When
        PaymentResponse actualResponse = paymentsAPIImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest);

        // Then
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(requestValidator).validateIMSTPaymentRequest(paymentRequest);
        verify(requestMapper).mapToPmtSvcRq(anyString(), anyString(), anyString(), any(), any(), anyString());
        verify(externalService).processPayment(anyString());
        verify(responseMapper).mapToPaymentResponse(anyString());
    }

    @Test
    public void testPayment_ATMSupportedTransaction_Success() throws Exception {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getTransactionInfo().setTransactionType("ATMTXN");
        paymentRequest.setAtmPayment(createValidATMPayment());
        PaymentResponse expectedResponse = createValidPaymentResponse();

        doNothing().when(requestValidator).validateATMPayment(paymentRequest);
        when(externalService.processATMPayment(any())).thenReturn(expectedResponse.getAtmPaymentResponse());

        // When
        PaymentResponse actualResponse = paymentsAPIImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest);

        // Then
        assertNotNull(actualResponse);
        assertNotNull(actualResponse.getAtmPaymentResponse());
        verify(requestValidator).validateATMPayment(paymentRequest);
        verify(externalService).processATMPayment(any());
    }

    @Test(expected = GenericRuntimeException.class)
    public void testPayment_ValidationFailure() throws Exception {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        doThrow(new GenericRuntimeException("Validation failed"))
                .when(requestValidator).validateIMSTPaymentRequest(paymentRequest);

        // When
        paymentsAPIImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest);

        // Then - exception expected
    }

    @Test(expected = JMSException.class)
    public void testPayment_JMSException() throws Exception {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        doNothing().when(requestValidator).validateIMSTPaymentRequest(paymentRequest);
        when(requestMapper.mapToPmtSvcRq(anyString(), anyString(), anyString(), any(), any(), anyString()))
                .thenReturn("<xml>mock request</xml>");
        when(externalService.processPayment(anyString())).thenThrow(new JMSException("MQ connection failed"));

        // When
        paymentsAPIImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest);

        // Then - exception expected
    }

    @Test(expected = UnsupportedEncodingException.class)
    public void testPayment_EncodingException() throws Exception {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        doNothing().when(requestValidator).validateIMSTPaymentRequest(paymentRequest);
        when(requestMapper.mapToPmtSvcRq(anyString(), anyString(), anyString(), any(), any(), anyString()))
                .thenThrow(new UnsupportedEncodingException("Encoding not supported"));

        // When
        paymentsAPIImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest);

        // Then - exception expected
    }

    @Test
    public void testPayment_UnsupportedTransactionType() throws Exception {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getTransactionInfo().setTransactionType("UNSUPPORTED");

        // When
        PaymentResponse actualResponse = paymentsAPIImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest);

        // Then
        assertNotNull(actualResponse);
        // Should return empty response for unsupported transaction types
        verifyNoInteractions(requestValidator);
        verifyNoInteractions(externalService);
    }

    @Test
    public void testPayment_NullPaymentRequest() throws Exception {
        // Given
        PaymentRequest paymentRequest = null;

        // When/Then
        try {
            paymentsAPIImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest);
            fail("Expected exception for null payment request");
        } catch (Exception e) {
            // Expected behavior
        }
    }

    @Test
    public void testPayment_EmptyHeaders() throws Exception {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();

        // When/Then
        try {
            paymentsAPIImpl.payment("", "", "", paymentRequest);
            fail("Expected exception for empty headers");
        } catch (Exception e) {
            // Expected behavior - header validation should catch this
        }
    }

    @Test
    public void testPayment_WithPromptPayInfo() throws Exception {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.setPromptPayInfo(createValidPromptPayInfo());
        PaymentResponse expectedResponse = createValidPaymentResponse();

        doNothing().when(requestValidator).validateIMSTPaymentRequest(paymentRequest);
        when(requestMapper.mapToPmtSvcRq(anyString(), anyString(), anyString(), any(), any(), anyString()))
                .thenReturn("<xml>mock request</xml>");
        when(externalService.processPayment(anyString())).thenReturn("<xml>mock response</xml>");
        when(responseMapper.mapToPaymentResponse(anyString())).thenReturn(expectedResponse);

        // When
        PaymentResponse actualResponse = paymentsAPIImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest);

        // Then
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(requestValidator).validateIMSTPaymentRequest(paymentRequest);
    }

    @Test
    public void testPayment_WithBillInfo() throws Exception {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().setBillInfo(createValidBillInfo());
        PaymentResponse expectedResponse = createValidPaymentResponse();

        doNothing().when(requestValidator).validateIMSTPaymentRequest(paymentRequest);
        when(requestMapper.mapToPmtSvcRq(anyString(), anyString(), anyString(), any(), any(), anyString()))
                .thenReturn("<xml>mock request</xml>");
        when(externalService.processPayment(anyString())).thenReturn("<xml>mock response</xml>");
        when(responseMapper.mapToPaymentResponse(anyString())).thenReturn(expectedResponse);

        // When
        PaymentResponse actualResponse = paymentsAPIImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest);

        // Then
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(requestValidator).validateIMSTPaymentRequest(paymentRequest);
    }

    @Test
    public void testPayment_DifferentPaymentInstrumentTypes() throws Exception {
        // Test different payment instrument types
        String[] instrumentTypes = {"ETFR", "CARD", "CASH", "CHQ"};
        
        for (String instrumentType : instrumentTypes) {
            // Given
            PaymentRequest paymentRequest = createValidPaymentRequest();
            paymentRequest.getImstPayment().getPaymentInfo().setPaymentInstrumentType(instrumentType);
            PaymentResponse expectedResponse = createValidPaymentResponse();

            doNothing().when(requestValidator).validateIMSTPaymentRequest(paymentRequest);
            when(requestMapper.mapToPmtSvcRq(anyString(), anyString(), anyString(), any(), any(), anyString()))
                    .thenReturn("<xml>mock request</xml>");
            when(externalService.processPayment(anyString())).thenReturn("<xml>mock response</xml>");
            when(responseMapper.mapToPaymentResponse(anyString())).thenReturn(expectedResponse);

            // When
            PaymentResponse actualResponse = paymentsAPIImpl.payment(reqUID, ownerID, sourceSystem, paymentRequest);

            // Then
            assertNotNull(actualResponse);
            assertEquals(expectedResponse, actualResponse);
        }
    }

    @Test
    public void testPayment_DifferentSourceSystems() throws Exception {
        // Test different source systems
        String[] sourceSystems = {"ENET", "PAYH", "CHAT", "IVR", "SDO"};
        
        for (String system : sourceSystems) {
            // Given
            PaymentRequest paymentRequest = createValidPaymentRequest();
            PaymentResponse expectedResponse = createValidPaymentResponse();

            doNothing().when(requestValidator).validateIMSTPaymentRequest(paymentRequest);
            when(requestMapper.mapToPmtSvcRq(anyString(), anyString(), eq(system), any(), any(), anyString()))
                    .thenReturn("<xml>mock request</xml>");
            when(externalService.processPayment(anyString())).thenReturn("<xml>mock response</xml>");
            when(responseMapper.mapToPaymentResponse(anyString())).thenReturn(expectedResponse);

            // When
            PaymentResponse actualResponse = paymentsAPIImpl.payment(reqUID, ownerID, system, paymentRequest);

            // Then
            assertNotNull(actualResponse);
            assertEquals(expectedResponse, actualResponse);
        }
    }

    // Helper methods for creating test data
    private ATMPayment createValidATMPayment() {
        ATMPayment atmPayment = new ATMPayment();
        // Set ATM payment specific fields
        return atmPayment;
    }

    private PromptPayInfo createValidPromptPayInfo() {
        PromptPayInfo promptPayInfo = new PromptPayInfo();
        // Set PromptPay specific fields
        return promptPayInfo;
    }

    private BillInfo createValidBillInfo() {
        BillInfo billInfo = new BillInfo();
        // Set bill info specific fields
        return billInfo;
    }
}
