package com.scb.api.ent.payments.integration.config;

import org.junit.Before;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

/**
 * Base class for integration tests providing common configuration and utilities
 * Sets up test environment with proper profiles and shared test utilities
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(locations = "classpath:application-test.yml")
public abstract class AbstractIntegrationTest {

    protected Random random;
    protected SimpleDateFormat dateFormat;

    @Before
    public void baseSetUp() {
        random = new Random();
        dateFormat = new SimpleDateFormat("yyyyMMdd");
    }

    /**
     * Generate a unique request UID for testing
     * @return unique request UID
     */
    protected String generateRequestUID() {
        Date dt = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmmssSSS");
        return sdf.format(dt) + (int) (Math.random() * 100000);
    }

    /**
     * Generate a unique UUID for testing
     * @return unique UUID string
     */
    protected String generateUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * Generate a random account number for testing
     * @return 10-digit account number
     */
    protected String generateAccountNumber() {
        return String.format("%010d", random.nextInt(**********));
    }

    /**
     * Generate current date in YYYYMMDD format
     * @return formatted date string
     */
    protected String getCurrentDate() {
        return dateFormat.format(new Date());
    }

    /**
     * Generate a random amount between 1 and 10000
     * @return random amount
     */
    protected double generateRandomAmount() {
        return Math.round((random.nextDouble() * 9999 + 1) * 100.0) / 100.0;
    }

    /**
     * Wait for a specified number of milliseconds
     * @param milliseconds time to wait
     */
    protected void waitFor(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Generate test terminal number
     * @return terminal number
     */
    protected String generateTerminalNumber() {
        String[] terminals = {"2550", "2551", "2552", "1111", "ws07"};
        return terminals[random.nextInt(terminals.length)];
    }

    /**
     * Generate test device ID
     * @return device ID
     */
    protected String generateDeviceId() {
        return String.format("%010d", random.nextInt(**********));
    }

    /**
     * Generate test resource owner ID
     * @return resource owner ID
     */
    protected String generateResourceOwnerId() {
        return "TEST_USER_" + random.nextInt(10000);
    }

    /**
     * Get random source system for testing
     * @return source system
     */
    protected String getRandomSourceSystem() {
        String[] systems = {"ENET", "PAYH", "CHAT", "IVR", "SDO", "STEL"};
        return systems[random.nextInt(systems.length)];
    }

    /**
     * Get random payment instrument type
     * @return payment instrument type
     */
    protected String getRandomPaymentInstrumentType() {
        String[] types = {"ETFR", "CARD", "CASH", "CHQ"};
        return types[random.nextInt(types.length)];
    }

    /**
     * Get random account type
     * @return account type
     */
    protected String getRandomAccountType() {
        String[] types = {"LNA", "SDA", "CDA", "TDA"};
        return types[random.nextInt(types.length)];
    }

    /**
     * Get random currency code
     * @return currency code
     */
    protected String getRandomCurrencyCode() {
        String[] codes = {"764", "840", "978", "392", "156", "826"};
        return codes[random.nextInt(codes.length)];
    }

    /**
     * Get random transaction type
     * @return transaction type
     */
    protected String getRandomTransactionType() {
        String[] types = {"PAYMENT", "REVPAYMENTTXN", "ATMTXN"};
        return types[random.nextInt(types.length)];
    }

    /**
     * Get random product code
     * @return product code
     */
    protected String getRandomProductCode() {
        String[] codes = {"PTNR", "LOAN", "CARD", "DEPO"};
        return codes[random.nextInt(codes.length)];
    }

    /**
     * Get random transaction code
     * @return transaction code
     */
    protected String getRandomTransactionCode() {
        String[] codes = {"LNPY", "CRDT", "DEBT", "TRAN"};
        return codes[random.nextInt(codes.length)];
    }

    /**
     * Generate test biller name
     * @return biller name
     */
    protected String generateBillerName() {
        String[] billers = {
            "Test Loan Company",
            "Sample Credit Card",
            "Mock Utility Bill",
            "Demo Insurance",
            "Test Telecom"
        };
        return billers[random.nextInt(billers.length)] + " " + random.nextInt(1000);
    }

    /**
     * Generate test annotation
     * @return annotation
     */
    protected String generateAnnotation() {
        return "Test payment annotation " + random.nextInt(10000);
    }

    /**
     * Generate deposit account from pattern
     * @return deposit account from
     */
    protected String generateDepositAcctFrom() {
        return "DSC-" + generateAccountNumber();
    }

    /**
     * Check if a string is null or empty
     * @param str string to check
     * @return true if null or empty
     */
    protected boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * Generate processing date (current date)
     * @return processing date
     */
    protected String generateProcessingDate() {
        return getCurrentDate();
    }

    /**
     * Generate payment value date (future date)
     * @return payment value date
     */
    protected String generatePaymentValueDate() {
        // Add 1 day to current date
        long tomorrow = System.currentTimeMillis() + (24 * 60 * 60 * 1000);
        return dateFormat.format(new Date(tomorrow));
    }

    /**
     * Generate processing time
     * @return processing time in HH:mm:ss format
     */
    protected String generateProcessingTime() {
        return String.format("%02d:%02d:%02d", 
                           random.nextInt(24), 
                           random.nextInt(60), 
                           random.nextInt(60));
    }

    /**
     * Generate OC code for testing
     * @return OC code
     */
    protected String generateOcCode() {
        String[] codes = {"3261", "3262", "3263", "3264"};
        return codes[random.nextInt(codes.length)];
    }

    /**
     * Generate post next day flag
     * @return post next day flag
     */
    protected String generatePostNextDayFlag() {
        return random.nextBoolean() ? "Y" : "N";
    }

    /**
     * Create a delay for testing timing scenarios
     * @param seconds seconds to delay
     */
    protected void delay(int seconds) {
        try {
            Thread.sleep(seconds * 1000L);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Generate test data with specific pattern
     * @param pattern pattern to follow
     * @param length desired length
     * @return generated string
     */
    protected String generateTestData(String pattern, int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            if (pattern.equals("NUMERIC")) {
                sb.append(random.nextInt(10));
            } else if (pattern.equals("ALPHA")) {
                sb.append((char) ('A' + random.nextInt(26)));
            } else if (pattern.equals("ALPHANUMERIC")) {
                if (random.nextBoolean()) {
                    sb.append(random.nextInt(10));
                } else {
                    sb.append((char) ('A' + random.nextInt(26)));
                }
            }
        }
        return sb.toString();
    }

    /**
     * Validate response time is within acceptable limits
     * @param startTime start time in milliseconds
     * @param endTime end time in milliseconds
     * @param maxAllowedMs maximum allowed time in milliseconds
     * @return true if within limits
     */
    protected boolean isResponseTimeAcceptable(long startTime, long endTime, long maxAllowedMs) {
        return (endTime - startTime) <= maxAllowedMs;
    }

    /**
     * Generate bulk test data
     * @param count number of items to generate
     * @param generator function to generate each item
     * @return array of generated items
     */
    protected String[] generateBulkTestData(int count, java.util.function.Supplier<String> generator) {
        String[] data = new String[count];
        for (int i = 0; i < count; i++) {
            data[i] = generator.get();
        }
        return data;
    }
}
