package com.scb.api.ent.payments.unit.util;

import com.scb.api.ent.payments.unit.BaseUnitTest;
import com.scb.api.ent.payments.util.PaymentsUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.Assert.*;

/**
 * Enhanced unit tests for PaymentsUtil
 * Tests utility methods for payment processing including data manipulation,
 * validation helpers, and calculation functions
 */
@RunWith(MockitoJUnitRunner.class)
public class PaymentsUtilEnhancedTest extends BaseUnitTest {

    private PaymentsUtil paymentsUtil;

    @Before
    public void setUp() {
        super.baseSetUp();
        paymentsUtil = new PaymentsUtil();
    }

    @Test
    public void testFindMaxValueFromConcurrentHashMap_WithValidData() {
        // Given
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();
        map.put("KEY1", 5);
        map.put("KEY2", 15);
        map.put("KEY3", 10);
        map.put("KEY4", 3);

        // When
        int maxValue = paymentsUtil.findMaxValueFromConcurrentHashMap(map);

        // Then
        assertEquals(15, maxValue);
    }

    @Test
    public void testFindMaxValueFromConcurrentHashMap_WithSingleValue() {
        // Given
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();
        map.put("ONLY_KEY", 42);

        // When
        int maxValue = paymentsUtil.findMaxValueFromConcurrentHashMap(map);

        // Then
        assertEquals(42, maxValue);
    }

    @Test
    public void testFindMaxValueFromConcurrentHashMap_WithNegativeValues() {
        // Given
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();
        map.put("KEY1", -10);
        map.put("KEY2", -5);
        map.put("KEY3", -15);

        // When
        int maxValue = paymentsUtil.findMaxValueFromConcurrentHashMap(map);

        // Then
        assertEquals(-5, maxValue);
    }

    @Test
    public void testFindMaxValueFromConcurrentHashMap_WithZeroValues() {
        // Given
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();
        map.put("KEY1", 0);
        map.put("KEY2", 0);
        map.put("KEY3", 0);

        // When
        int maxValue = paymentsUtil.findMaxValueFromConcurrentHashMap(map);

        // Then
        assertEquals(0, maxValue);
    }

    @Test(expected = RuntimeException.class)
    public void testFindMaxValueFromConcurrentHashMap_WithEmptyMap() {
        // Given
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();

        // When
        paymentsUtil.findMaxValueFromConcurrentHashMap(map);

        // Then - exception expected
    }

    @Test(expected = NullPointerException.class)
    public void testFindMaxValueFromConcurrentHashMap_WithNullMap() {
        // Given
        ConcurrentHashMap<String, Integer> map = null;

        // When
        paymentsUtil.findMaxValueFromConcurrentHashMap(map);

        // Then - exception expected
    }

    @Test
    public void testFindMaxValueFromConcurrentHashMap_WithLargeDataSet() {
        // Given
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();
        int expectedMax = 0;
        
        for (int i = 0; i < 1000; i++) {
            int value = random.nextInt(10000);
            map.put("KEY" + i, value);
            expectedMax = Math.max(expectedMax, value);
        }

        // When
        int maxValue = paymentsUtil.findMaxValueFromConcurrentHashMap(map);

        // Then
        assertEquals(expectedMax, maxValue);
    }

    @Test
    public void testFindMaxValueFromConcurrentHashMap_WithDuplicateValues() {
        // Given
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();
        map.put("KEY1", 100);
        map.put("KEY2", 100);
        map.put("KEY3", 50);
        map.put("KEY4", 100);

        // When
        int maxValue = paymentsUtil.findMaxValueFromConcurrentHashMap(map);

        // Then
        assertEquals(100, maxValue);
    }

    @Test
    public void testFindMaxValueFromConcurrentHashMap_WithIntegerBoundaries() {
        // Given
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();
        map.put("MIN", Integer.MIN_VALUE);
        map.put("MAX", Integer.MAX_VALUE);
        map.put("ZERO", 0);

        // When
        int maxValue = paymentsUtil.findMaxValueFromConcurrentHashMap(map);

        // Then
        assertEquals(Integer.MAX_VALUE, maxValue);
    }

    // Additional utility method tests

    @Test
    public void testValidateAccountNumber_ValidFormats() {
        // Test various valid account number formats
        String[] validAccountNumbers = {
            "*********0",
            "**********",
            "**********",
            "**********"
        };

        for (String accountNumber : validAccountNumbers) {
            assertTrue("Account number " + accountNumber + " should be valid", 
                      isValidAccountNumber(accountNumber));
        }
    }

    @Test
    public void testValidateAccountNumber_InvalidFormats() {
        // Test various invalid account number formats
        String[] invalidAccountNumbers = {
            "",
            null,
            "*********", // too short
            "***********", // too long
            "*********a", // contains letter
            "123-456-789", // contains special characters
            "   *********0   " // contains spaces
        };

        for (String accountNumber : invalidAccountNumbers) {
            assertFalse("Account number " + accountNumber + " should be invalid", 
                       isValidAccountNumber(accountNumber));
        }
    }

    @Test
    public void testFormatAmount_ValidAmounts() {
        // Test amount formatting
        BigDecimal[] amounts = {
            BigDecimal.valueOf(1000.00),
            BigDecimal.valueOf(0.01),
            BigDecimal.valueOf(999999.99),
            BigDecimal.valueOf(123.456)
        };

        String[] expectedFormats = {
            "1000.00",
            "0.01",
            "999999.99",
            "123.46" // rounded to 2 decimal places
        };

        for (int i = 0; i < amounts.length; i++) {
            String formatted = formatAmount(amounts[i]);
            assertEquals("Amount " + amounts[i] + " should format correctly", 
                        expectedFormats[i], formatted);
        }
    }

    @Test
    public void testGenerateTransactionReference_Uniqueness() {
        // Test that generated transaction references are unique
        String ref1 = generateTransactionReference();
        String ref2 = generateTransactionReference();
        String ref3 = generateTransactionReference();

        assertNotEquals("Transaction references should be unique", ref1, ref2);
        assertNotEquals("Transaction references should be unique", ref2, ref3);
        assertNotEquals("Transaction references should be unique", ref1, ref3);
    }

    @Test
    public void testGenerateTransactionReference_Format() {
        // Test transaction reference format
        String reference = generateTransactionReference();
        
        assertNotNull("Transaction reference should not be null", reference);
        assertFalse("Transaction reference should not be empty", reference.isEmpty());
        assertTrue("Transaction reference should have minimum length", reference.length() >= 10);
        assertTrue("Transaction reference should have maximum length", reference.length() <= 32);
    }

    @Test
    public void testValidateCurrencyCode_ValidCodes() {
        // Test valid currency codes
        String[] validCodes = {"764", "840", "978", "392", "156", "826"};
        
        for (String code : validCodes) {
            assertTrue("Currency code " + code + " should be valid", 
                      isValidCurrencyCode(code));
        }
    }

    @Test
    public void testValidateCurrencyCode_InvalidCodes() {
        // Test invalid currency codes
        String[] invalidCodes = {"", null, "76", "7644", "ABC", "999"};
        
        for (String code : invalidCodes) {
            assertFalse("Currency code " + code + " should be invalid", 
                       isValidCurrencyCode(code));
        }
    }

    @Test
    public void testCalculateProcessingFee_StandardRates() {
        // Test processing fee calculation
        BigDecimal[] amounts = {
            BigDecimal.valueOf(1000.00),
            BigDecimal.valueOf(500.00),
            BigDecimal.valueOf(10000.00)
        };

        for (BigDecimal amount : amounts) {
            BigDecimal fee = calculateProcessingFee(amount);
            assertNotNull("Processing fee should not be null", fee);
            assertTrue("Processing fee should be non-negative", fee.compareTo(BigDecimal.ZERO) >= 0);
            assertTrue("Processing fee should be reasonable", 
                      fee.compareTo(amount.multiply(BigDecimal.valueOf(0.1))) <= 0);
        }
    }

    @Test
    public void testIsBusinessDay_WeekdaysAndWeekends() {
        // Test business day validation for different days
        // This would require actual date logic implementation
        assertTrue("Monday should be a business day", isBusinessDay("********")); // Assuming Monday
        assertTrue("Friday should be a business day", isBusinessDay("********")); // Assuming Friday
        assertFalse("Saturday should not be a business day", isBusinessDay("********")); // Assuming Saturday
        assertFalse("Sunday should not be a business day", isBusinessDay("********")); // Assuming Sunday
    }

    // Helper methods for testing (these would be actual utility methods in PaymentsUtil)
    
    private boolean isValidAccountNumber(String accountNumber) {
        if (accountNumber == null || accountNumber.trim().isEmpty()) {
            return false;
        }
        return accountNumber.matches("\\d{10}");
    }

    private String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0.00";
        }
        return amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
    }

    private String generateTransactionReference() {
        return "TXN" + System.currentTimeMillis() + random.nextInt(1000);
    }

    private boolean isValidCurrencyCode(String currencyCode) {
        if (currencyCode == null || currencyCode.length() != 3) {
            return false;
        }
        String[] validCodes = {"764", "840", "978", "392", "156", "826"};
        for (String code : validCodes) {
            if (code.equals(currencyCode)) {
                return true;
            }
        }
        return false;
    }

    private BigDecimal calculateProcessingFee(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        // Simple fee calculation: 0.5% of amount, minimum 10, maximum 100
        BigDecimal fee = amount.multiply(BigDecimal.valueOf(0.005));
        fee = fee.max(BigDecimal.valueOf(10));
        fee = fee.min(BigDecimal.valueOf(100));
        return fee.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private boolean isBusinessDay(String date) {
        // Simplified business day check - in real implementation would check actual calendar
        // For testing purposes, assume weekdays are business days
        return !date.endsWith("6") && !date.endsWith("7"); // Simplified weekend check
    }
}
