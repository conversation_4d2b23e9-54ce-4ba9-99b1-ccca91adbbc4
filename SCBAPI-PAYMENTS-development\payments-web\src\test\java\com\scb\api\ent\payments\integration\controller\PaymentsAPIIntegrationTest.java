package com.scb.api.ent.payments.integration.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scb.api.ent.payments.PaymentRequest;
import com.scb.api.ent.payments.PaymentResponse;
import com.scb.api.ent.payments.integration.config.AbstractIntegrationTest;
import com.scb.api.ent.payments.testdata.PaymentMockDataGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.*;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * Integration tests for PaymentsAPI endpoints
 * Tests full request-response flow including validation, processing, and external service integration
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
public class PaymentsAPIIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private HttpHeaders headers;
    private String baseUrl;

    @Before
    public void setUp() {
        headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("requestUID", generateRequestUID());
        headers.add("resourceOwnerID", "INTEGRATION_TEST_USER");
        headers.add("sourceSystem", "ENET");
        
        baseUrl = "/v1/payment";
    }

    @Test
    public void testPayment_ValidLoanPayment_Success() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateLoanPaymentScenario();
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, headers);

        // When
        ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, PaymentResponse.class);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getImstPaymentResponse());
        assertNotNull(response.getBody().getImstPaymentResponse().getAccountId());
        assertNotNull(response.getBody().getImstPaymentResponse().getProcessingDate());
    }

    @Test
    public void testPayment_ValidCardPayment_Success() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateCardPaymentScenario();
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, headers);

        // When
        ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, PaymentResponse.class);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getImstPaymentResponse());
    }

    @Test
    public void testPayment_ValidCashPayment_Success() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateCashPaymentScenario();
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, headers);

        // When
        ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, PaymentResponse.class);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getImstPaymentResponse());
    }

    @Test
    public void testPayment_HighValuePayment_Success() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateHighValuePaymentScenario();
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, headers);

        // When
        ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, PaymentResponse.class);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getImstPaymentResponse());
    }

    @Test
    public void testPayment_ForeignCurrency_Success() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateForeignCurrencyScenario();
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, headers);

        // When
        ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, PaymentResponse.class);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getImstPaymentResponse());
    }

    @Test
    public void testPayment_InvalidRequest_BadRequest() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateInvalidPaymentRequest("NULL_TRANSACTION_INFO");
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, headers);

        // When
        ResponseEntity<String> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, String.class);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    public void testPayment_InvalidAmount_BadRequest() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateInvalidPaymentRequest("INVALID_AMOUNT");
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, headers);

        // When
        ResponseEntity<String> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, String.class);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    public void testPayment_InvalidAccountNumber_BadRequest() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateInvalidPaymentRequest("INVALID_ACCOUNT_NUMBER");
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, headers);

        // When
        ResponseEntity<String> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, String.class);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    public void testPayment_MissingHeaders_BadRequest() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateValidPaymentRequest();
        HttpHeaders emptyHeaders = new HttpHeaders();
        emptyHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, emptyHeaders);

        // When
        ResponseEntity<String> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, String.class);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    public void testPayment_InvalidSourceSystem_BadRequest() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateValidPaymentRequest();
        HttpHeaders invalidHeaders = new HttpHeaders();
        invalidHeaders.setContentType(MediaType.APPLICATION_JSON);
        invalidHeaders.add("requestUID", generateRequestUID());
        invalidHeaders.add("resourceOwnerID", "TEST_USER");
        invalidHeaders.add("sourceSystem", "INVALID_SYSTEM");
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, invalidHeaders);

        // When
        ResponseEntity<String> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, String.class);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    public void testPayment_BoundaryAmounts_Success() throws Exception {
        // Test minimum amount
        PaymentRequest minAmountRequest = PaymentMockDataGenerator.generateBoundaryAmountPaymentRequest("MIN_AMOUNT");
        HttpEntity<PaymentRequest> minEntity = new HttpEntity<>(minAmountRequest, headers);
        
        ResponseEntity<PaymentResponse> minResponse = restTemplate.postForEntity(
                baseUrl + "/billPayments", minEntity, PaymentResponse.class);
        
        assertEquals(HttpStatus.OK, minResponse.getStatusCode());

        // Test maximum amount
        PaymentRequest maxAmountRequest = PaymentMockDataGenerator.generateBoundaryAmountPaymentRequest("MAX_AMOUNT");
        HttpEntity<PaymentRequest> maxEntity = new HttpEntity<>(maxAmountRequest, headers);
        
        ResponseEntity<PaymentResponse> maxResponse = restTemplate.postForEntity(
                baseUrl + "/billPayments", maxEntity, PaymentResponse.class);
        
        assertEquals(HttpStatus.OK, maxResponse.getStatusCode());
    }

    @Test
    public void testPayment_SpecialCharacters_Success() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateSpecialCharacterPaymentRequest();
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, headers);

        // When
        ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, PaymentResponse.class);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    public void testPayment_DifferentSourceSystems_Success() throws Exception {
        String[] sourceSystems = {"ENET", "PAYH", "CHAT", "IVR"};
        
        for (String sourceSystem : sourceSystems) {
            // Given
            PaymentRequest request = PaymentMockDataGenerator.generateValidPaymentRequest();
            HttpHeaders systemHeaders = new HttpHeaders();
            systemHeaders.setContentType(MediaType.APPLICATION_JSON);
            systemHeaders.add("requestUID", generateRequestUID());
            systemHeaders.add("resourceOwnerID", "TEST_USER");
            systemHeaders.add("sourceSystem", sourceSystem);
            HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, systemHeaders);

            // When
            ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
                    baseUrl + "/billPayments", entity, PaymentResponse.class);

            // Then
            assertEquals("Source system " + sourceSystem + " should work", 
                        HttpStatus.OK, response.getStatusCode());
            assertNotNull(response.getBody());
        }
    }

    @Test
    public void testPayment_ConcurrentRequests_Success() throws Exception {
        // Test concurrent payment processing
        int numberOfThreads = 5;
        Thread[] threads = new Thread[numberOfThreads];
        boolean[] results = new boolean[numberOfThreads];

        for (int i = 0; i < numberOfThreads; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    PaymentRequest request = PaymentMockDataGenerator.generateValidPaymentRequest();
                    HttpHeaders threadHeaders = new HttpHeaders();
                    threadHeaders.setContentType(MediaType.APPLICATION_JSON);
                    threadHeaders.add("requestUID", generateRequestUID());
                    threadHeaders.add("resourceOwnerID", "CONCURRENT_TEST_" + threadIndex);
                    threadHeaders.add("sourceSystem", "ENET");
                    HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, threadHeaders);

                    ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
                            baseUrl + "/billPayments", entity, PaymentResponse.class);

                    results[threadIndex] = response.getStatusCode() == HttpStatus.OK;
                } catch (Exception e) {
                    results[threadIndex] = false;
                }
            });
        }

        // Start all threads
        for (Thread thread : threads) {
            thread.start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }

        // Verify all requests succeeded
        for (int i = 0; i < numberOfThreads; i++) {
            assertTrue("Concurrent request " + i + " should succeed", results[i]);
        }
    }

    @Test
    public void testPayment_ResponseTimePerformance() throws Exception {
        // Given
        PaymentRequest request = PaymentMockDataGenerator.generateValidPaymentRequest();
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, headers);

        // When
        long startTime = System.currentTimeMillis();
        ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, PaymentResponse.class);
        long endTime = System.currentTimeMillis();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        long responseTime = endTime - startTime;
        assertTrue("Response time should be under 5 seconds", responseTime < 5000);
    }

    @Test
    public void testPayment_Idempotency() throws Exception {
        // Given - same request UID for both requests
        String requestUID = generateRequestUID();
        PaymentRequest request = PaymentMockDataGenerator.generateValidPaymentRequest();
        
        HttpHeaders idempotentHeaders = new HttpHeaders();
        idempotentHeaders.setContentType(MediaType.APPLICATION_JSON);
        idempotentHeaders.add("requestUID", requestUID);
        idempotentHeaders.add("resourceOwnerID", "IDEMPOTENCY_TEST");
        idempotentHeaders.add("sourceSystem", "ENET");
        
        HttpEntity<PaymentRequest> entity = new HttpEntity<>(request, idempotentHeaders);

        // When - make the same request twice
        ResponseEntity<PaymentResponse> response1 = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, PaymentResponse.class);
        
        ResponseEntity<PaymentResponse> response2 = restTemplate.postForEntity(
                baseUrl + "/billPayments", entity, PaymentResponse.class);

        // Then - both should succeed (or second should be handled appropriately)
        assertEquals(HttpStatus.OK, response1.getStatusCode());
        // The second response behavior depends on the idempotency implementation
        assertTrue("Second response should be OK or handled appropriately", 
                  response2.getStatusCode() == HttpStatus.OK || 
                  response2.getStatusCode() == HttpStatus.CONFLICT);
    }
}
