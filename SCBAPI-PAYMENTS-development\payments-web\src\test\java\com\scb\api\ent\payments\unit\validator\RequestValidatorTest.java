package com.scb.api.ent.payments.unit.validator;

import com.scb.api.common.framework.exception.GenericRuntimeException;
import com.scb.api.ent.payments.PaymentRequest;
import com.scb.api.ent.payments.unit.BaseUnitTest;
import com.scb.api.ent.payments.validator.v1.RequestValidator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.*;

/**
 * Unit tests for RequestValidator
 * Tests validation logic for payment requests including field validation,
 * business rules, and error handling
 */
@RunWith(MockitoJUnitRunner.class)
public class RequestValidatorTest extends BaseUnitTest {

    @InjectMocks
    private RequestValidator requestValidator;

    @Before
    public void setUp() {
        super.baseSetUp();
    }

    @Test
    public void testValidateIMSTPaymentRequest_ValidRequest_Success() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();

        // When/Then - should not throw exception
        try {
            requestValidator.validateIMSTPaymentRequest(paymentRequest);
        } catch (Exception e) {
            fail("Valid request should not throw exception: " + e.getMessage());
        }
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_NullRequest() {
        // Given
        PaymentRequest paymentRequest = null;

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_NullTransactionInfo() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.setTransactionInfo(null);

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_NullIMSTPayment() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.setImstPayment(null);

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidTransactionType() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getTransactionInfo().setTransactionType("");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidProductCode() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getTransactionInfo().setProductCode("");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidTransactionCode() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getTransactionInfo().setTransactionCode("");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_NullTransactionOwner() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().setTransactionOwner(null);

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidTerminalNumber() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getTransactionOwner().setTerminalNumber("");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidDeviceId() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getTransactionOwner().setDeviceId("");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_NullPaymentInfo() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().setPaymentInfo(null);

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_NullPaymentAmount() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getPaymentInfo().setPaymentAmount(null);

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_ZeroPaymentAmount() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getPaymentInfo().setPaymentAmount(BigDecimal.ZERO);

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_NegativePaymentAmount() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getPaymentInfo().setPaymentAmount(BigDecimal.valueOf(-100));

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidPaymentInstrumentType() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getPaymentInfo().setPaymentInstrumentType("INVALID");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_NullDepositAccountInfo() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getPaymentInfo().setDepositAccountInfo(null);

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidDepositAccountId() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getPaymentInfo().getDepositAccountInfo().setAccountId("");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_NullBillerInfo() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().setBillerInfo(null);

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_NullBillerAccountInfo() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getBillerInfo().setBillerAccountInfo(null);

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidBillerAccountId() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getBillerInfo().getBillerAccountInfo().setAccountId("");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidBillerAccountType() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getBillerInfo().getBillerAccountInfo().setAccountType("");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidBillerAccountCurrency() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getBillerInfo().getBillerAccountInfo().setAccountCurrency("");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidBillerName() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().getBillerInfo().setBillerName("");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test(expected = GenericRuntimeException.class)
    public void testValidateIMSTPaymentRequest_InvalidProcessingDate() {
        // Given
        PaymentRequest paymentRequest = createValidPaymentRequest();
        paymentRequest.getImstPayment().setProcessingDate("invalid-date");

        // When
        requestValidator.validateIMSTPaymentRequest(paymentRequest);

        // Then - exception expected
    }

    @Test
    public void testValidateIMSTPaymentRequest_ValidPaymentInstrumentTypes() {
        // Test all valid payment instrument types
        String[] validTypes = {"ETFR", "CARD", "CASH", "CHQ"};
        
        for (String type : validTypes) {
            // Given
            PaymentRequest paymentRequest = createValidPaymentRequest();
            paymentRequest.getImstPayment().getPaymentInfo().setPaymentInstrumentType(type);

            // When/Then - should not throw exception
            try {
                requestValidator.validateIMSTPaymentRequest(paymentRequest);
            } catch (Exception e) {
                fail("Valid payment instrument type " + type + " should not throw exception: " + e.getMessage());
            }
        }
    }

    @Test
    public void testValidateIMSTPaymentRequest_ValidAccountTypes() {
        // Test all valid account types
        String[] validTypes = {"LNA", "SDA", "CDA", "TDA"};
        
        for (String type : validTypes) {
            // Given
            PaymentRequest paymentRequest = createValidPaymentRequest();
            paymentRequest.getImstPayment().getBillerInfo().getBillerAccountInfo().setAccountType(type);

            // When/Then - should not throw exception
            try {
                requestValidator.validateIMSTPaymentRequest(paymentRequest);
            } catch (Exception e) {
                fail("Valid account type " + type + " should not throw exception: " + e.getMessage());
            }
        }
    }

    @Test
    public void testValidateIMSTPaymentRequest_ValidCurrencyCodes() {
        // Test all valid currency codes
        String[] validCodes = {"764", "840", "978", "392"};
        
        for (String code : validCodes) {
            // Given
            PaymentRequest paymentRequest = createValidPaymentRequest();
            paymentRequest.getImstPayment().getBillerInfo().getBillerAccountInfo().setAccountCurrency(code);

            // When/Then - should not throw exception
            try {
                requestValidator.validateIMSTPaymentRequest(paymentRequest);
            } catch (Exception e) {
                fail("Valid currency code " + code + " should not throw exception: " + e.getMessage());
            }
        }
    }

    @Test
    public void testValidateIMSTPaymentRequest_BoundaryAmounts() {
        // Test boundary amounts
        BigDecimal[] amounts = {
            BigDecimal.valueOf(0.01), // Minimum valid amount
            BigDecimal.valueOf(999999.99), // Maximum valid amount
            BigDecimal.valueOf(1000.00) // Common amount
        };
        
        for (BigDecimal amount : amounts) {
            // Given
            PaymentRequest paymentRequest = createValidPaymentRequest();
            paymentRequest.getImstPayment().getPaymentInfo().setPaymentAmount(amount);

            // When/Then - should not throw exception
            try {
                requestValidator.validateIMSTPaymentRequest(paymentRequest);
            } catch (Exception e) {
                fail("Valid amount " + amount + " should not throw exception: " + e.getMessage());
            }
        }
    }
}
